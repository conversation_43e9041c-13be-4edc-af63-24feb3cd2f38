#!/bin/bash

# ==============================================================================
# Alastor 完整测试流程管理脚本 v3.0 (支持多种攻击)
#
# 功能：
# 1. 自动部署完整应用栈 (cc-db, attackserver, functions)
# 2. 启动增强的日志收集服务器，并自动选择端口
# 3. 通过命令行参数执行指定类型的负载测试
# 4. 支持多种攻击类型组合测试
# 5. 自动收集、整理和分析日志
# 6. 确保任何失败都有详细日志
# ==============================================================================

set -e -o pipefail

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_header() { echo -e "${PURPLE}[ALASTOR]${NC} $1"; }
log_step() { echo -e "${CYAN}[STEP]${NC} $1"; }

# 🚨 全局错误处理函数
handle_error() {
    local exit_code=$?
    local lineno=$1
    local command=$2
    
    echo ""
    log_error "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
    log_error "!!         脚本执行失败，提前退出         !!"
    log_error "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
    log_error "退出代码: $exit_code"
    log_error "出错行号: $lineno"
    log_error "失败命令: $command"
    echo ""
    log_error "--- 调用堆栈 ---"
    local i=0
    local a_caller
    while a_caller=($(caller $i)); do
        if [ ${#a_caller[@]} -lt 3 ]; then break; fi
        log_error "$(printf '  -> %s:%s in function %s' "${a_caller[2]}" "${a_caller[0]}" "${a_caller[1]}")"
        i=$((i+1))
    done
    log_error "--------------------"
    
    # 脚本将在这里退出，EXIT陷阱会负责清理
}

# 🧹 全局变量存储需要清理的资源
CLEANUP_FILESERVER_PID=""
CLEANUP_KUBECTL_PIDS=()
CLEANUP_TEMP_FILES=()

# 🚨 资源清理函数
cleanup_resources() {
    local exit_code=$?
    echo "🧹 [$(date '+%H:%M:%S')] 开始清理资源..."
    
    # 停止fileserver进程
    if [[ -n "$CLEANUP_FILESERVER_PID" ]] && kill -0 "$CLEANUP_FILESERVER_PID" 2>/dev/null; then
        echo "🔌 停止fileserver进程 (PID: $CLEANUP_FILESERVER_PID)"
        kill "$CLEANUP_FILESERVER_PID" 2>/dev/null || true
        # 等待进程退出
        for i in {1..5}; do
            if ! kill -0 "$CLEANUP_FILESERVER_PID" 2>/dev/null; then
                break
            fi
            sleep 1
        done
        # 强制杀死如果还在运行
        kill -9 "$CLEANUP_FILESERVER_PID" 2>/dev/null || true
        echo "✅ fileserver进程已停止"
    fi
    
    # 停止后台kubectl进程
    for pid in "${CLEANUP_KUBECTL_PIDS[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            echo "🔌 停止kubectl进程 (PID: $pid)"
            kill "$pid" 2>/dev/null || true
        fi
    done
    
    # 清理临时文件
    for temp_file in "${CLEANUP_TEMP_FILES[@]}"; do
        if [[ -f "$temp_file" ]]; then
            echo "🗑️ 删除临时文件: $temp_file"
            rm -f "$temp_file" || true
        fi
    done
    
    # 清理端口文件
    rm -f .fileserver_port 2>/dev/null || true
    
    echo "✅ [$(date '+%H:%M:%S')] 资源清理完成 (退出代码: $exit_code)"
    # 保留退出代码
    if [ "$exit_code" -ne 0 ]; then
        log_error "脚本因错误而终止。"
    fi
}

# 🚨 注册信号处理器
# EXIT陷阱总是在脚本退出时执行，无论正常还是异常
trap cleanup_resources EXIT
# ERR陷阱在任何命令返回非零退出代码时执行 (因为 set -e)
trap 'handle_error $LINENO "$BASH_COMMAND"' ERR
# 中断和终止信号也由EXIT处理
trap 'log_warning "收到SIGINT信号，开始清理..."; exit 130' SIGINT
trap 'log_warning "收到SIGTERM信号，开始清理..."; exit 143' SIGTERM

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_header() { echo -e "${PURPLE}[ALASTOR]${NC} $1"; }
log_step() { echo -e "${CYAN}[STEP]${NC} $1"; }

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"

# 全局配置
LOGS_BASE_DIR="$PROJECT_ROOT/logs"
DEFAULT_FILESERVER_PORT=44445
OPENFAAS_URL="http://127.0.0.1:31112"
HEY_CMD="$PROJECT_ROOT/zjy-performance/hey_linux_amd64"
PYTHON_CMD="/home/<USER>/miniconda3/envs/alastor/bin/python"  # 使用conda环境

# 测试会话配置
TEST_SESSION_ID=""
TEST_SESSION_DIR=""
FILESERVER_PID=""
FILESERVER_PORT=""
CLEANUP_REQUIRED=false

# 依赖检查
check_dependencies() {
    log_step "1/7: 检查依赖..."
    local missing_deps=()
    for cmd in kubectl faas-cli python3 curl tar; do
        if ! command -v "$cmd" &> /dev/null; then missing_deps+=("$cmd"); fi
    done
    if [ ! -f "$HEY_CMD" ] && ! command -v hey &> /dev/null; then missing_deps+=("hey"); fi
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少依赖: ${missing_deps[*]}"; exit 1
    fi
    if [ ! -f "$HEY_CMD" ]; then HEY_CMD="hey"; fi
    log_success "依赖检查完成"
}

# 生成测试会话ID
generate_session_id() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local deployment_type="$1"
    local attack_types_str="$2"
    local request_rate="$3"
    local duration="$4"
    local mode="$5"
    
    # 将攻击类型列表转换为字符串（最多显示前3个）
    local attack_display="${attack_types_str// /_}"
    if [[ $(echo "$attack_types_str" | wc -w) -gt 3 ]]; then
        attack_display="$(echo "$attack_types_str" | awk '{print $1 "_" $2 "_" $3}')_plus"
    fi
    
    TEST_SESSION_ID="${timestamp}_${deployment_type}_${attack_display}_${mode}_${request_rate}rps"
    TEST_SESSION_DIR="$LOGS_BASE_DIR/$TEST_SESSION_ID"
    
    mkdir -p "$TEST_SESSION_DIR"
    log_info "测试会话: $TEST_SESSION_ID"
    log_info "日志目录: $TEST_SESSION_DIR"
}

# 配置环境变量
setup_environment() {
    log_step "配置环境变量..."
    
    # 获取主机IP
    local host_ip=$(hostname -I | awk '{print $1}')
    if [ -z "$host_ip" ]; then
        host_ip="*************"  # 默认值
        log_warning "无法自动获取主机IP，使用默认值: $host_ip"
    else
        log_info "检测到主机IP: $host_ip"
    fi
    
    # 保存配置到会话目录
    cat > "$TEST_SESSION_DIR/test-config.env" <<EOF
# Alastor 测试会话配置
TEST_SESSION_ID=$TEST_SESSION_ID
HOST_IP=$host_ip
FILESERVER_PORT=$FILESERVER_PORT
DEPLOYMENT_TYPE=$DEPLOYMENT_TYPE
ATTACK_TYPES=$ATTACK_TYPES
REQUEST_RATE=$REQUEST_RATE
DURATION=$DURATION
MODE=$MODE
TIMESTAMP=$(date -Iseconds)
EOF
    
    export SERVER_HOST="$host_ip"
    export FILESERVER_PORT="$FILESERVER_PORT"
    
    log_success "环境配置完成"
}

# 启动文件服务器
start_fileserver() {
    log_step "2/7: 启动日志收集服务器..."
    local fileserver_script="$PROJECT_ROOT/enhanced-fileserver.py"
    if [ ! -f "$fileserver_script" ]; then
        log_error "未找到文件服务器脚本: $fileserver_script"; exit 1
    fi
    
    # 检查Python环境
    if [ ! -f "$PYTHON_CMD" ]; then
        log_warning "未找到conda Python环境，使用系统Python"
        PYTHON_CMD="python3"
    fi
    
    # 直接使用会话目录作为文件接收目录，不创建子目录
    local incoming_dir="$TEST_SESSION_DIR/raw_logs"
    mkdir -p "$incoming_dir"
    
    # 设置环境变量
    export FILESERVER_TARGET_DIR="$incoming_dir"
    export FILESERVER_PORT="$DEFAULT_FILESERVER_PORT"
    
    # 先测试能否找到可用端口
    log_info "寻找可用端口..."
    local port_test_output
    port_test_output=$($PYTHON_CMD -c "
import socket
import os
start_port = int(os.environ.get('FILESERVER_PORT', 44445))
for port in range(start_port, start_port + 100):
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', port))
            print(port)
            break
    except OSError:
        continue
else:
    print('ERROR')
" 2>/dev/null)
    
    if [ "$port_test_output" = "ERROR" ] || [ -z "$port_test_output" ]; then
        log_error "无法找到可用端口"; exit 1
    fi
    
    FILESERVER_PORT="$port_test_output"
    export FILESERVER_PORT
    log_info "将使用端口: $FILESERVER_PORT"
    
    # 在后台启动文件服务器
    log_info "启动文件服务器..."
    ($PYTHON_CMD "$fileserver_script" "$incoming_dir" > "$TEST_SESSION_DIR/fileserver.log" 2>&1) &
    FILESERVER_PID=$!
    CLEANUP_FILESERVER_PID=$FILESERVER_PID  # 🧹 注册到清理列表
    
    # 等待启动并验证
    sleep 3
    if ! kill -0 "$FILESERVER_PID" 2>/dev/null; then
        log_error "文件服务器启动失败。查看日志: $TEST_SESSION_DIR/fileserver.log"
        cat "$TEST_SESSION_DIR/fileserver.log"
        exit 1
    fi
    
    log_success "文件服务器已启动 (PID: $FILESERVER_PID, Port: $FILESERVER_PORT)"
    echo "$FILESERVER_PID" > "$TEST_SESSION_DIR/fileserver.pid"
    CLEANUP_REQUIRED=true
}

# 部署应用
deploy_application() {
    local deployment_type="$1"
    log_step "3/7: 部署应用: $deployment_type"
    
    # 部署核心服务 (DB, attackserver)
    log_info "部署核心服务..."
    kubectl apply -f "$PROJECT_ROOT/kube-yamls/cc-db.yaml"
    kubectl apply -f "$PROJECT_ROOT/kube-yamls/attackserver.yaml"
    
    # 部署函数
    local yaml_file=""
    case "$deployment_type" in
        "hello-retail-scale") yaml_file="$PROJECT_ROOT/kube-yamls/hello-retail.yaml";;
        "hello-retail-single") yaml_file="$PROJECT_ROOT/kube-yamls/HR-singlepod.yaml";;
        "vanilla-scale") yaml_file="$PROJECT_ROOT/kube-yamls/hello-retail-vanilla.yaml";;
        "vanilla-single") yaml_file="$PROJECT_ROOT/kube-yamls/HR-vanilla-singlepod.yaml";;
        *) log_error "未知部署类型: $deployment_type"; exit 1;;
    esac
    
    if [ ! -f "$yaml_file" ]; then log_error "部署文件不存在: $yaml_file"; exit 1; fi
    
    # 创建临时YAML文件，动态注入正确的环境变量
    local temp_yaml="$TEST_SESSION_DIR/deployment_with_env.yaml"
    local host_ip
    host_ip=$(hostname -I | awk '{print $1}')
    local server_host="${host_ip:-*************}"
    
    log_info "更新YAML文件环境变量..."
    log_info "  SERVER_HOST: $server_host"
    log_info "  FILESERVER_PORT: $FILESERVER_PORT"
    
    # 使用sed替换环境变量
    sed -e "s/SERVER_HOST: '[^']*'/SERVER_HOST: '$server_host'/g" \
        -e "s/FILESERVER_PORT: '[^']*'/FILESERVER_PORT: '$FILESERVER_PORT'/g" \
        "$yaml_file" > "$temp_yaml"
    
    log_info "部署函数: $yaml_file (已更新环境变量)"
    cp "$temp_yaml" "$TEST_SESSION_DIR/deployment.yaml"
    
    # 🔧 新增：检查是否需要部署
    log_info "检查现有函数Pod..."
    local expected_pods=4
    local running_pods
    running_pods=$(kubectl get pods -n openfaas-fn --no-headers=true 2>/dev/null | grep "product-purchase" | grep -c "Running" || true)

    if [ "$running_pods" -ge "$expected_pods" ]; then
        log_success "✓ 检测到 $running_pods 个Pod已在运行。跳过函数部署。"
    else
        log_info "检测到 $running_pods/$expected_pods 个Pod在运行，继续函数部署..."
        # 设置环境变量并部署
        export SERVER_HOST="$server_host"
        export FILESERVER_PORT
        
        log_info "执行: faas-cli deploy -f $temp_yaml"
        if ! faas-cli deploy -f "$temp_yaml"; then
            log_error "faas-cli deploy 失败！"
            log_info "这通常意味着OpenFaaS网关没有响应或YAML文件有语法错误。"
            log_info "检查faas-cli状态:"
            faas-cli list || log_warning "无法连接到OpenFaaS网关"
            exit 1 # 明确退出，将触发ERR和EXIT陷阱
        fi
        log_success "faas-cli deploy 命令执行成功"
    fi
    
    log_info "等待函数启动..."
    # 智能Pod状态检查 - 等待所有期望的pods运行
    for i in {1..60}; do # 增加超时到120秒
        # 全局错误陷阱会处理此命令的失败
        local kubectl_output
        kubectl_output=$(kubectl get pods -n openfaas-fn --no-headers=true)

        # 使用 grep -c 和 || true 来安全地计数，避免在找不到时因pipefail退出循环
        local running_pods
        running_pods=$(echo "$kubectl_output" | grep "product-purchase" | grep -c "Running" || true)
        
        echo "  [第 $i/60 次检查] Running pods: $running_pods/$expected_pods"
        
        if [ "$running_pods" -eq "$expected_pods" ]; then
            log_success "✓ 所有 $expected_pods 个Pod都已成功运行!"
            break
        fi

        # 如果Pod未全部运行，则显示详细状态以供调试
        if [ "$running_pods" -lt "$expected_pods" ]; then
            echo "    Pod当前状态详情:"
            # 显示所有相关Pod的状态，特别是那些非Running的
            echo "$kubectl_output" | grep "product-purchase" | sed 's/^/      -> /'
        fi
        
        # 检查是否已到循环末尾
        if [ $i -eq 60 ]; then
            log_error "⚠ 超时: 120秒后仍有Pod未就绪"
            log_info "这是最终的Pod状态:"
            echo "$kubectl_output" | sed 's/^/    /'
            exit 1 # 明确失败退出
        fi
        
        sleep 2
    done
    log_success "应用部署完成"
}

# a new function to collect log snapshots
collect_app_logs_snapshot() {
    log_step "5/7: 收集应用日志快照..."
    local app_logs_dir="$TEST_SESSION_DIR/app_logs"
    mkdir -p "$app_logs_dir"

    log_info "为所有函数Pod收集日志..."
    local functions=("product-purchase" "product-purchase-get-price" "product-purchase-authorize-cc" "product-purchase-publish")
    
    for func in "${functions[@]}"; do
        # 获取该函数的所有Pod名称
        local pod_names
        pod_names=$(kubectl get pods -n openfaas-fn -l "faas_function=$func" -o jsonpath='{.items[*].metadata.name}' 2>/dev/null || true)
        
        if [ -n "$pod_names" ]; then
            for pod_name in $pod_names; do
                log_info "  -> 正在收集 $func (Pod: $pod_name) 的日志..."
                kubectl logs "$pod_name" -n openfaas-fn > "$app_logs_dir/${pod_name}.log" 2>/dev/null || log_warning "    无法收集Pod $pod_name 的日志"
            done
        else
            log_warning "未找到函数 $func 的任何Pod"
        fi
    done

    # 收集核心服务日志
    log_info "收集核心服务日志..."
    local core_services=("deployment/cc-db" "deployment/attackserver")
    for service in "${core_services[@]}"; do
        local service_name
        service_name=$(basename "$service")
        log_info "  -> 正在收集 $service_name 的日志..."
        kubectl logs "$service" -n openfaas-fn > "$app_logs_dir/${service_name}.log" 2>/dev/null || log_warning "    无法收集服务 $service_name 的日志"
    done

    log_success "应用日志快照收集完成"
}

# 解析攻击类型和依赖关系
parse_attack_sequence() {
    local attack_input="$1"
    local -n result_array=$2
    
    # 定义攻击依赖关系
    declare -A attack_dependencies=(
        ["attack2"]="attack1"
        ["escape_s2"]="escape_s1"
    )
    
    # 将输入按空格分割成数组
    IFS=' ' read -ra attacks <<< "$attack_input"
    
    # 检查并添加依赖
    local final_attacks=()
    for attack in "${attacks[@]}"; do
        # 检查是否有依赖
        if [[ -n "${attack_dependencies[$attack]}" ]]; then
            local dep="${attack_dependencies[$attack]}"
            # 如果依赖不在列表中，添加它
            if [[ ! " ${attacks[*]} " =~ " $dep " ]]; then
                final_attacks+=("$dep")
            fi
        fi
        final_attacks+=("$attack")
    done
    
    # 去重并保持顺序
    local unique_attacks=()
    local seen=()
    for attack in "${final_attacks[@]}"; do
        if [[ ! " ${seen[*]} " =~ " $attack " ]]; then
            unique_attacks+=("$attack")
            seen+=("$attack")
        fi
    done
    
    result_array=("${unique_attacks[@]}")
}

# 生成混合请求文件（支持多种攻击）
generate_mixed_requests() {
    local mode="$1"
    local duration="$2"
    local benign_counts=("${@:3}")
    
    local benign_file="$PROJECT_ROOT/requests/benign.json"
    local mixed_file="$TEST_SESSION_DIR/mixed_requests.jsonl"
    local load_output="$TEST_SESSION_DIR/load_test_output.jsonl"
    
    # 验证良性请求文件
    if ! jq -e . "$benign_file" >/dev/null 2>&1; then
        log_error "无效JSON文件: $benign_file"
        exit 1
    fi
    
    # 收集所有攻击文件并验证
    local attack_files=()
    for attack_type in "${ATTACK_TYPES[@]}"; do
        local attack_file="$PROJECT_ROOT/requests/$attack_type.json"
        if [ ! -f "$attack_file" ]; then
            log_error "攻击文件不存在: $attack_file"
            exit 1
        fi
        if ! jq -e . "$attack_file" >/dev/null 2>&1; then
            log_error "无效JSON文件: $attack_file"
            exit 1
        fi
        attack_files+=("$attack_file")
    done
    
    # 生成请求序列
    {
        if [ "$mode" = "sequential" ]; then
            # 顺序模式: benign_counts[0] -> attack1 -> benign_counts[1] -> attack2 -> ...
            local attack_index=0
            for ((i=0; i<${#benign_counts[@]}; i++)); do
                # 添加良性请求
                for ((j=0; j<${benign_counts[$i]} j++)); do
                    cat "$benign_file"; echo
                done
                
                # 如果不是最后一个分隔符，添加攻击
                if [ $i -lt ${#ATTACK_TYPES[@]} ]; then
                    cat "${attack_files[$attack_index]}"; echo
                    ((attack_index++))
                fi
            done
        else
            # 混合模式: 所有攻击随机插入到良性请求中
            local total_benign=${benign_counts[0]}
            local total_attacks=${#ATTACK_TYPES[@]}
            local total_requests=$((total_benign + total_attacks))
            
            # 创建攻击位置数组
            local positions=()
            for ((i=0; i<total_requests; i++)); do
                positions+=($i)
            done
            
            # 随机打乱位置
            for ((i=0; i<total_requests; i++)); do
                local j=$((RANDOM % total_requests))
                local temp=${positions[$i]}
                positions[$i]=${positions[$j]}
                positions[$j]=$temp
            done
            
            # 标记攻击位置
            local attack_positions=()
            for ((i=0; i<total_attacks; i++)); do
                attack_positions+=(${positions[$i]})
            done
            
            # 排序攻击位置
            IFS=$'\n' attack_positions=($(sort -n <<<"${attack_positions[*]}"))
            unset IFS
            
            # 生成请求序列
            local attack_index=0
            for ((i=0; i<total_requests; i++)); do
                if [[ $attack_index -lt ${#attack_positions[@]} && $i -eq ${attack_positions[$attack_index]} ]]; then
                    cat "${attack_files[$attack_index]}"; echo
                    ((attack_index++))
                else
                    cat "$benign_file"; echo
                fi
            done
        fi
    } | jq -c . > "$mixed_file"
    
    echo "$mixed_file"
}

# 执行负载测试（支持多种攻击）
execute_load_test() {
    local mode="$1"
    local duration="$2"
    shift 2
    
    local benign_counts=("$@")
    
    # 生成混合请求文件
    local mixed_file=$(generate_mixed_requests "$mode" "$duration" "${benign_counts[@]}")
    local load_output="$TEST_SESSION_DIR/load_test_output.jsonl"
    
    # 计算请求总数
    local total_requests=$(wc -l < "$mixed_file")
    
    # 计算请求间隔（精确到毫秒）
    local duration_seconds=${duration%s}
    if [[ "$duration" =~ m$ ]]; then
        duration_seconds=$(( ${duration%m} * 60 ))
    fi
    local interval=$(echo "scale=6; $duration_seconds/$total_requests" | bc -l)
    
    # 发送请求（带实时日志）
    log_info "开始发送 $total_requests 个请求（间隔 ${interval}s）"
    awk '{print NR " " $0}' "$mixed_file" | while read -r idx request; do
        log_info "发送请求 $idx/$total_requests"
        if ! curl -sS -X POST \
          -H "Content-Type: application/json" \
          -d "$request" \
          "$OPENFAAS_URL/function/product-purchase" | jq -c '.' >> "$load_output"; then
            log_error "请求 $idx 失败"
        fi
        sleep "$interval"
    done
    
    log_success "负载测试完成"
    log_info "响应日志: $load_output"
}

# cleanup_application function refactored for robustness
cleanup_application() {
    log_step "6/7: 清理应用..."
    bash $PROJECT_ROOT/tearHR.sh
    
    log_info "等待核心服务Pod终止..."
    sleep 5 # 等待Pod终止
    
    log_success "应用清理完成"
}

# analyze_logs function
analyze_logs() {
    log_step "8/8: 分析日志..."
    
    local processor_script="$PROJECT_ROOT/alastor-log-processor.sh"
    if [ ! -f "$processor_script" ]; then
        log_warning "未找到日志处理器脚本，跳过分析"; return
    fi
    
    log_info "调用日志处理器..."
    bash "$processor_script" "$TEST_SESSION_DIR"
    log_success "日志分析完成"
}

# 显示帮助
show_help() {
    cat <<EOF
Alastor 完整测试流程管理脚本 v3.0 (支持多种攻击)

用法: $0 <部署类型> <攻击类型列表> [测试时长] [模式参数] [--keep-app]

参数:
  部署类型:
    hello-retail-scale   - Hello Retail (可扩展)
    hello-retail-single  - Hello Retail (单Pod) [默认]
    vanilla-scale        - Vanilla版本 (可扩展)
    vanilla-single       - Vanilla版本 (单Pod)

  攻击类型列表:
    支持单个或多个攻击类型，用空格分隔
    例如: "attack1" 或 "attack1 attack2 cfattack"
    支持的攻击类型:
    benign | attack1 | attack2 | cfattack | sas6 | sas3 | escape_s1 | escape_s2 | readfiles | sqlinjection | deserialize | uploadfile

  测试时长: 测试持续时间, 如 30s, 1m (默认: 30s)

模式参数:
  --sequential <benign1> [benign2] [benign3] ... - 顺序模式，指定每个攻击之间的良性请求数
  --mixed <total_benign> - 混合模式，指定总良性请求数，攻击随机插入

可选标志:
  --keep-app          - 测试结束后不删除应用容器，方便调试。
  -h, --help           - 显示此帮助信息。

示例:
  # 单个攻击
  $0 hello-retail-single attack1
  $0 hello-retail-single attack1 60s --sequential 10 20
  
  # 多个攻击顺序执行
  $0 hello-retail-single "attack1 attack2" 60s --sequential 10 5 15
  
  # 多个攻击混合执行
  $0 hello-retail-single "attack1 cfattack escape_s1" 60s --mixed 50
  
  # 自动处理依赖关系（attack2需要attack1）
  $0 hello-retail-single attack2 60s --sequential 10 20
  
  # 复杂组合
  $0 hello-retail-single "escape_s1 escape_s2 cfattack" 90s --mixed 100

注意:
  - 对于有依赖关系的攻击（如attack2需要attack1，escape_s2需要escape_s1），
    系统会自动添加缺失的依赖攻击
  - 顺序模式下，良性请求数参数数量 = 攻击数量 + 1
  - 混合模式下，只需要一个良性请求总数参数
EOF
}

# 主函数
main() {
    # 默认值
    local keep_apps=false
    local mode="sequential"  # 默认顺序模式
    local benign_counts=()
    local attack_types_input=""
    local duration="30s"

    # 参数解析
    local args=()
    while [[ $# -gt 0 ]]; do
        case "$1" in
            --sequential)
                mode="sequential"
                shift
                # 收集所有数字参数
                while [[ $# -gt 0 && "$1" =~ ^[0-9]+$ ]]; do
                    benign_counts+=("$1")
                    shift
                done
                ;;
            --mixed)
                mode="mixed"
                shift
                if [[ $# -gt 0 && "$1" =~ ^[0-9]+$ ]]; then
                    benign_counts+=("$1")
                    shift
                else
                    log_error "混合模式需要指定总良性请求数"
                    exit 1
                fi
                ;;
            --keep-app)
                keep_apps=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                args+=("$1")
                shift
                ;;
        esac
    done
    set -- "${args[@]}"

    # 必需参数检查
    if [ "$#" -lt 2 ]; then
        log_error "缺少必需参数: <deployment_type> <attack_type_list>"
        show_help
        exit 1
    fi

    local deployment_type="$1"
    attack_types_input="$2"
    duration="${3:-30s}"

    # 验证 duration 格式
    if ! [[ "$duration" =~ ^[0-9]+[sm]$ ]]; then
        log_error "测试时长格式错误，请使用如 '30s' 或 '1m' 的格式"
        exit 1
    fi

    # 解析攻击序列（处理依赖关系）
    parse_attack_sequence "$attack_types_input" ATTACK_TYPES
    
    # 验证攻击类型
    for attack in "${ATTACK_TYPES[@]}"; do
        if [ ! -f "$PROJECT_ROOT/requests/$attack.json" ]; then
            log_error "不支持的攻击类型: $attack"
            exit 1
        fi
    done

    # 设置默认良性请求数
    if [ ${#benign_counts[@]} -eq 0 ]; then
        if [ "$mode" = "sequential" ]; then
            # 为每个攻击和前后设置默认值
            for ((i=0; i<=${#ATTACK_TYPES[@]}; i++)); do
                benign_counts+=(10)
            done
        else
            benign_counts=(50)  # 混合模式默认50个良性请求
        fi
    fi

    # 验证参数数量
    if [ "$mode" = "sequential" ]; then
        local expected_counts=$((${#ATTACK_TYPES[@]} + 1))
        if [ ${#benign_counts[@]} -ne $expected_counts ]; then
            log_error "顺序模式下，良性请求数参数数量应为 $expected_counts (攻击数+1)，但提供了 ${#benign_counts[@]}"
            exit 1
        fi
    elif [ "$mode" = "mixed" ]; then
        if [ ${#benign_counts[@]} -ne 1 ]; then
            log_error "混合模式下，只需要一个良性请求总数参数"
            exit 1
        fi
    fi

    # 计算总请求数和动态频率
    local total_attacks=${#ATTACK_TYPES[@]}
    local total_benign=0
    if [ "$mode" = "sequential" ]; then
        for count in "${benign_counts[@]}"; do
            total_benign=$((total_benign + count))
        done
    else
        total_benign=${benign_counts[0]}
    fi
    
    local total_requests=$((total_benign + total_attacks))
    local duration_seconds=${duration%s}
    if [[ "$duration" =~ m$ ]]; then
        duration_seconds=$(( ${duration%m} * 60 ))
    fi
    local request_rate=$(echo "scale=2; $total_requests / $duration_seconds" | bc -l)

    # 生成会话ID
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local attack_types_str="${ATTACK_TYPES[*]}"
    local attack_display="${attack_types_str// /_}"
    if [[ ${#ATTACK_TYPES[@]} -gt 3 ]]; then
        attack_display="$(echo "$attack_types_str" | awk '{print $1 "_" $2 "_" $3}')_plus"
    fi
    
    local benign_display=""
    if [ "$mode" = "sequential" ]; then
        benign_display="seq$(IFS='-'; echo "${benign_counts[*]}")"
    else
        benign_display="mix${benign_counts[0]}"
    fi
    
    TEST_SESSION_ID="${timestamp}_${deployment_type}_${attack_display}_${mode}_${benign_display}_${duration}"
    TEST_SESSION_DIR="$LOGS_BASE_DIR/$TEST_SESSION_ID"
    mkdir -p "$TEST_SESSION_DIR"
    log_info "测试会话: $TEST_SESSION_ID"
    log_info "日志目录: $TEST_SESSION_DIR"

    # 设置全局变量
    DEPLOYMENT_TYPE="$deployment_type"
    ATTACK_TYPES=("${ATTACK_TYPES[@]}")
    DURATION="$duration"
    REQUEST_RATE="$request_rate"
    MODE="$mode"

    # 显示测试配置
    log_info "=== 测试配置 ==="
    log_info "部署类型: $deployment_type"
    log_info "攻击序列: ${ATTACK_TYPES[*]}"
    log_info "测试模式: $mode"
    log_info "测试时长: $duration"
    log_info "总请求数: $total_requests (良性: $total_benign, 攻击: $total_attacks)"
    log_info "请求频率: $request_rate rps"
    if [ "$mode" = "sequential" ]; then
        log_info "顺序分布: ${benign_counts[*]}"
    else
        log_info "混合分布: 总良性 $total_benign 个，攻击随机插入"
    fi

    # 依赖检查
    check_dependencies

    # 启动流程
    start_fileserver
    setup_environment
    deploy_application "$deployment_type"
    
    # 执行负载测试
    execute_load_test "$mode" "$duration" "${benign_counts[@]}"

    collect_app_logs_snapshot

    if [ "$keep_apps" = false ]; then
        cleanup_application
    fi

    analyze_logs
    
    log_success "测试完成. 会话ID: $TEST_SESSION_ID"
    log_info "报告目录: $TEST_SESSION_DIR"
}

# 运行主函数
main "$@"