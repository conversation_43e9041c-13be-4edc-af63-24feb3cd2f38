# Alastor：对无服务器入侵溯源的重建

**Alastor** 通过在容器内集成**系统调用追踪(strace)**和**网络流量拦截(mitmproxy)**，实现对函数运行时行为的全面监控和分析。

## 🎯 系统原理

**Alastor**通过修改OpenFaaS的**of-watchdog**组件，在每个函数容器中集成了两个关键监控工具：

1. **🔍 strace系统调用追踪**
   - 捕获部分系统调用：文件操作、网络连接、进程创建等
   - 生成`request.alastor.*`日志文件
   - 用于检测文件下载、数据库访问等异常行为

2. **🌐 mitmproxy网络流量拦截**
   - 通过设置 `http_proxy` 和 `https_proxy` 环境变量，实现**应用级代理**来拦截网络流量
   - 生成 `nettaint.log` 日志文件，包含网络请求信息，并表现为溯源图中的连边。

- 在容器被删除时，日志会自动发送到主机的特定端口（需要主机事先监听特定端口，并以`FILESERVER_PORT`环境变量的形式传递给应用）

## 🚀 快速开始

### 环境准备

```bash
conda create -n alastor python=3.12 -y
conda activate alastor  
pip install -r env/minimal-requirements.txt
```
**环境说明：** 包含Python 3.12 + 数据处理包(pandas, numpy) + 可视化包(matplotlib)。

### 运行测试

#### 混合benign+attack测试（新功能）
```bash
conda activate alastor
bash tearHR.sh

# 顺序模式：a次良性请求 + 1次攻击请求 + b次良性请求
bash alastor-test-runner-new.sh hello-retail-single sas3 5s --sequential 10 5

# 随机混合模式：x次良性请求 + 1次攻击请求随机混合
bash alastor-test-runner-new.sh hello-retail-single deserialize 5s --mixed 20

# 参数说明:
#   hello-retail-single: 使用带恶意代码的hello retail，单Pod部署模式
#   sas3/deserialize: 攻击类型
#   5s: 测试总时长
#   --sequential 10 5: 顺序模式，10次良性 + 1次攻击 + 5次良性
#   --mixed 20: 随机混合模式，20次良性 + 1次攻击随机混合
#### 🆕 多种攻击组合测试（v3.0增强功能）
```
**单次测试支持多种攻击类型组合**，无需多次执行脚本：

```bash
# 多个攻击顺序执行
bash alastor-test-runner-new.sh hello-retail-single "attack1 attack2" 60s --sequential 10 5 15
# 序列：10个良性 → attack1 → 5个良性 → attack2 → 15个良性

# 多个攻击混合执行（按输入顺序随机插入）
bash alastor-test-runner-new.sh hello-retail-single "attack1 cfattack escape_s1" 90s --mixed 100
# 100个良性请求中按attack1→cfattack→escape_s1顺序随机插入攻击
```

**新增参数格式：**
- 攻击类型列表：空格分隔，如 `"attack1 attack2 cfattack"`
- `--sequential a b c...`：参数数量=攻击数+1
- `--mixed N`：N个良性请求中随机插入所有攻击（按输入顺序）


#### 传统单类型测试
```bash
# 激活conda环境
conda activate alastor
# 每次实验前都删除已有的函数pod。之后可以用kubectl get pods -n openfaas-fn | grep 'product-purchase'来检查。
bash tearHR.sh
# 两步攻击：热容器复用执行sqldump
bash alastor-test-runner.sh hello-retail-single attack1 1 1s --keep-app ; bash alastor-test-runner.sh hello-retail-single attack2 1 1s
# 单步：benign（良性流程）, cfattack（函数调用流）, sas3, deserialize, readfiles。直接替换下面指令中的"cfattack"即可执行对应的流程
bash alastor-test-runner.sh hello-retail-single cfattack 1 1s

# 参数说明:
#   hello-retail-single: 使用带恶意代码的hello retail，单Pod部署模式
#   attack1: 攻击类型（热容器复用的第一步）
#   1: 每秒1个请求的负载强度
#   1s: 测试持续时间
#   keep-app：测试后不删除pod（用于热容器复用）
```



##### 输出目录结构
- 日志目录命名规则：`logs/<timestamp>_<deployment_type>_<attack_type>_<模式>_<duration>`
  - 例如：`logs/20250629_145519_hello-retail-single_attack1_seq10-1-5_30s`（顺序模式）
  - 例如：`logs/20250629_150000_hello-retail-single_attack2_mix20_30s`（随机模式）
- 其他内容与传统测试一致（参考传统单类型测试的输出目录说明）。

##### 注意事项
1. **请求频率**：脚本会根据总请求数和总时长自动计算请求间隔，无需手动指定。
2. **日志完整性**：确保测试完成后检查 `load_test_output.jsonl` 和 `mixed_requests.jsonl` 文件。

#### 仅绘图
```bash
# 仅根据已有日志绘图。log processor脚本会重新编译graph/alastor，确保绘图效果最新。
bash alastor-log-processor.sh logs/20250629_145519_hello-retail-single_attack2_1rps
```

### 输出目录结构
- 每一次测试对应的日志位于`logs/<timestamp>_<params>`，比如`logs/20250628_153111_hello-retail-single_attack2_1rps`。
- 其中大部分文件夹内容都是无效的，只需要关注`logs/<timestamp>_<params>`中的以下内容：
  - `reports/dot`目录下有**全局溯源图和局部溯源图**；其中全局溯源图是`global_local_based.dot`。
  - `processed_logs`目录下是收到的strace日志和`nettaint.log`网络日志；
  - `app_logs`目录下是应用日志；
  - `analysis/`目录包含分析结果；
  - `test-config.env`是本次实验配置，`deployment_with_env.yaml`是本次实验部署hello retail所用的yaml文件。
- 如果没有删除pod（比如`attack1`使用了`--keep-app`参数，那么不会收集到strace和mitmproxy日志，仅有`app_logs`是有效的）

### 一些其他文件的功能
- `alastor-log-processor.sh`用于处理日志，会被主脚本`alastor-test-runner.sh`调用。该脚本会自动：
  - 编译graph/alastor程序
  - 生成局部图和全局溯源图（global_local_based.dot）
  - 转换为PNG图片（如果安装了graphviz）
  - 提供彩色输出和进度提示
- `check-port.sh`用于检查当前占用特定端口的进程
- `log.sh`可以查看pod的应用日志
- `tearHR.sh`用于删除hello retail的4个函数pod（如果手动部署，则需要执行`faas-cli deploy -f xxx.yaml`，注意正确设置`FILESERVER_PORT`环境变量）

### 重新构建
- of-watchdog，以及hello retail四个函数的代码均位于openfaas-attack-funcs目录下。原版位于openfaas-attack-funcs-vanilla目录下，目前没有使用。
1. 修改应用内容后需要重新构建4个函数的镜像并push到docker hub
```bash
cd openfaas-attack-funcs
bash build-images.sh
```
2. 修改of-watchdog之后需要重新构建of-watchdog的镜像，再构建应用函数镜像并push到docker hub
```bash
cd openfaas-attack-funcs
# 注意，build of-watchdog需要go环境
bash build-of-watchdog.sh
bash build-images.sh
```
3. 修改绘图日志处理和绘图部分后，需要重新编译graph/alastor。但这部分已经集成到了`alastor-log-processor.sh`中，直接运行即可。

### 手动测试
- `conda activate alastor`
1. 运行`python3 enhanced-fileserver.py test/raw_logs`，让容器把日志发到test文件夹中。
2. 新开一个shell，运行`faas-cli deploy -f kube-yamls/HR-singlepod.yaml`（或其他配置）
3. 执行`curl -d @requests/attack1.json -H "Content-Type: application/json" -X POST 'http://localhost:31112/function/product-purchase'`发送请求
4. 执行`bash log.sh <function-name>`查看应用日志。
5. `bash tearHR.sh`删除pod，之后用`bash alastor-log-processor.sh test`解析收集到的日志。

---

## 攻击检测场景

**Hello Retail**：

```
客户端请求 → product-purchase (主函数)
    ├── product-purchase-get-price     (价格查询)
    ├── product-purchase-authorize-cc  (信用卡验证) ⚠️ 恶意代码注入点
    └── product-purchase-publish       (结果发布)
```

**支持的攻击类型**：

**基础攻击类型**：
- `benign`: **正常业务流量** - 标准的购买流程
- `attack1`: **前置步骤** - 恶意函数下载外部脚本
- `attack2`: **数据导出攻击** - 执行attack1下载的脚本
- `cfattack`：**越权攻击** - 改变函数调用链，不执行get-price和auth-cc，直接Publish

**新增类型**：
- `sas3`: **命令注入** 。应该可以看到open 2.txt和3.txt
```javascript
// fileName = "2.txt #\n echo \"123\" > 3.txt"
child_process.execSync(`touch ${fileName}`);
```

- `deserialize`: **反序列化攻击**：目前能捕获到了"execve /bin/ls"，但观察不到ls的返回内容，可能和strace配置相关。
```javascript
// payload = "{\"function\":\"_$$ND_FUNC$$_function(){return require('child_process').execSync('ls').toString().trim();}()\"}"
const result = require(node-serialize).unserialize(payload);
```

- `readfiles`: **批量文件读取** - 同时读取多个系统文件，可以看到open /etc/passwd, /etc/hosts, /home/<USER>/handler.js
```javascript
// filesToRead = "[\"/etc/passwd\", \"/etc/hosts\", \"/home/<USER>/handler.js\"]"
const results = await Promise.all(filesToRead.map(async (file) => {
  try {
  	const content = await fsPromises.readFile(file, 'utf8');
  	return { file, content };
  } catch (fileErr) {
  	console.error(`读取文件 ${file} 失败:`, fileErr);
  	return { file, content: null, error: fileErr.message };
  }
  }));
```

- `escape_s1`: **容器逃逸步骤1** - 下载逃逸脚本。原理和attack1相同。
- `escape_s2`: **容器逃逸步骤2** - 执行逃逸攻击，部署函数为pod。目前尚未成功，可能和alastor的代理有关

**其他类型（和已有类型重复，未使用）**：
- `sas6`: **任意文件读取** - 读取系统敏感文件（如/etc/passwd），被readfiles和sas3包含
- `sqlinjection`: **SQL注入攻击** - 实际上目前没有设计注入，只是依次执行了两条SQL语句。而执行语句的过程位于cc-db中而不是函数容器中，alastor对此没有感知，只能捕获到与cc-db的连接`connect(22, {sa_family=AF_INET, sin_port=htons(3306), sin_addr=inet_addr("10.152.183.36")}, 16) = -1 EINPROGRESS (Operation in progress)`。
- `uploadfile`: **恶意文件上传**：也是touch, echo, cat等指令的结合

---

## 🔧 详细执行流程

### 脚本执行的步骤

#### **Step 1/7: 检查依赖**
验证运行环境是否具备必要工具：
- `kubectl` - Kubernetes集群管理
- `faas-cli` - OpenFaaS函数部署
- `python3` - 日志服务器和数据分析
- `hey` - HTTP负载测试工具

#### **Step 2/7: 启动日志收集服务器**
- 启动Python文件服务器监听指定端口（默认44445）
- 容器内的监控组件会将日志文件上传到此服务器
- 自动端口检测避免冲突

#### **Step 3/7: 部署应用**
- 部署核心服务：`cc-db`(数据库)、`attackserver`(攻击模拟服务器)。如果尚未部署，需要执行`kubectl apply -f kube-yamls/attackserver.yaml`。
- 根据配置部署函数：
  - `hello-retail-single`: 单Pod模式，便于调试
  - `hello-retail-scale`: 多Pod模式，模拟生产环境
- 动态注入环境变量（文件服务器地址、端口等）
- 等待所有4个函数Pod进入Running状态

#### **Step 4/7: 执行负载测试**
- 使用`hey`工具向`product-purchase`函数发送HTTP请求
- 根据攻击类型加载不同的JSON负载：
  ```json
  // attack1.json - 触发恶意下载
  {
    "productId": "123",
    "quantity": 1,
    "malicious": "one"  // 触发恶意代码路径
  }
  ```
- 同时收集所有Pod的应用日志用于调试

#### **Step 5/7: 清理应用**
- 删除所有部署的函数和服务
- 等待Pod完全终止，确保最后的日志被上传
- 清理Kubernetes资源

#### **Step 6/7: 收集和整理日志**
- 从文件服务器接收目录收集所有`.tar.gz`日志文件
- 按Pod名称解压到独立目录，避免文件覆盖
- 收集最终的应用日志快照
- 生成日志统计摘要

#### **Step 7/7: 分析日志（未调试）**
调用`alastor-log-processor.sh`进行深度分析：

**🔬 系统调用分析**：
- 统计系统调用频率，识别异常调用模式
- 分析进程活动时间线，检测可疑进程
- 生成DeepLog机器学习训练数据

**🌐 网络流量分析**：
- 解析HTTP请求/响应，提取访问的URL
- 统计网络连接数，识别异常外联
- 检测数据泄露特征

**📊 可视化生成**：
- 生成系统调用频率柱状图
- 创建进程活动时间线图
- 生成函数调用依赖关系图（DOT格式）

**📄 报告生成**：
- 综合分析报告（Markdown格式）
- 包含测试配置、统计摘要、异常发现
- 提供后续分析建议

## �️ 高级配置

### 自定义攻击场景

1. **创建新的请求文件**：
   ```bash
   # 在requests/目录下创建custom.json
   {
     "productId": "456",
     "customPayload": "malicious_data"
   }
   ```

2. **运行自定义测试**：
   ```bash
   # 传统方式
   bash alastor-test-runner.sh hello-retail-single custom 5 60s
   
   # 混合方式
   bash alastor-test-runner-mixed.sh hello-retail-single 50 custom 60s
   ```

### 批量测试脚本

```bash
#!/bin/bash
# 批量运行不同攻击类型的混合测试
for attack in attack1 attack2 sas3 deserialize; do
    bash alastor-test-runner-mixed.sh hello-retail-single 100 $attack 60s
    sleep 60  # 等待系统恢复
done
```

## 📊 混合负载测试详解

### 测试模式对比

| 模式 | 描述 | 使用场景 |
|------|------|----------|
| **随机混合** | 在总请求中随机分布attack请求 | 模拟真实攻击场景 |
| **顺序模式** | benign → attack → benign | 对比分析攻击前后行为 |
| **攻击优先** | attack → benign | 研究攻击的即时影响 |

### 参数说明
- **benign次数**: 良性请求数量，建议50-500次
- **attack类型**: 支持的攻击类型见上文
- **总时间**: 测试持续时间，建议30s-5m
- **发送模式**: 控制请求的发送顺序

### 结果分析
混合测试会生成：
- `mixed_requests.jsonl` - 实际发送的请求序列
- `load_test_output.txt` - hey工具的详细输出
- 与传统测试相同的日志结构

---

**注意**：混合负载测试是新功能，建议先使用传统测试验证环境，再使用混合测试进行复杂场景分析。
