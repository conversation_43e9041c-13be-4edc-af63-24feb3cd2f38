apiVersion: v1
kind: Service
metadata:
  labels:
    name: attackserver
  name: attackserver
  namespace: openfaas-fn
spec:
  ports:
  - name: attackserver
    port: 8888
    targetPort: 80
  #clusterIP: None
  selector:
    app: attackserver
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: attackserver
  namespace: openfaas-fn
spec:
  replicas: 1
  selector:
    matchLabels:
      app: attackserver
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: attackserver
    spec:
      hostname: attackserver
      containers:
      - image: koinikki/attackserver:latest # 7.17 yyc modified from dattapubali
        name: attackserver
        ports:
        - containerPort: 80
          name: attackserver
        resources: {}
      restartPolicy: Always
status: {}