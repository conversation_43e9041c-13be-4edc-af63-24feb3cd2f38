# Alastor 网络连接分析和修复方案

## 🔍 问题分析

### 1. NetPeer节点问题分析

**当前问题**：
```dot
"product_purchase_authorize_cc_5cf7df585c_mbp5c_30" [ label="NetPeer##attackserver.openfaas-fn.svc.cluster.local:8888", shape="diamond" ];
"product_purchase_authorize_cc_5cf7df585c_mbp5c_31" [ label="NetPeer##127.0.0.1:35274", shape="diamond" ];
```

**问题根源**：
1. **局部图生成**：在 `alastor.go` 中，每个容器都会为其网络连接创建 NetPeer 节点
2. **全局图合并**：在 `local_graph_based_builder.go` 中，这些局部 NetPeer 节点被直接复制到全局图
3. **连接逻辑缺失**：没有正确处理 `127.0.0.1:35274` 到 `attackserver:8888` 的连接关系

### 2. 网络连接原理分析

**实际网络流程**：
```
Container Process → Socket(127.0.0.1:35274) → [通过3000端口代理] → attackserver:8888
```

**当前图表示**：
```
Container Process → NetPeer##127.0.0.1:35274  (断开)
Container Process → NetPeer##attackserver:8888  (直接连接，错误)
```

**正确的图表示应该是**：
```
Container Process → Socket(0.0.0.0:3000) → Gateway → attackserver:8888
```

### 3. sas6攻击系统调用缺失问题

**预期行为**：
```javascript
// handler.js:69
const data = fs.readFileSync(path, 'utf8');
```

**应该产生的系统调用**：
- `open("/etc/passwd", O_RDONLY)` 
- `read(fd, buffer, size)`

**可能的问题**：
1. 攻击请求没有正确到达 authorize-cc 函数
2. 函数执行了但系统调用没有被 strace 捕获
3. 日志收集有问题

## 🛠️ 解决方案

### 修复1：网络连接逻辑重构

**目标**：正确表示网络连接的层次结构

**修改策略**：
1. **局部图**：只保留 Socket 节点，移除直接的 NetPeer 节点
2. **全局图**：通过 nettaint.log 解析真实的网络连接
3. **连接映射**：建立 Socket → Gateway → 外部服务的连接

### 修复2：系统调用捕获增强

**目标**：确保文件读取系统调用被正确捕获

**检查点**：
1. strace 是否正确启动
2. 攻击请求是否到达目标函数
3. 系统调用过滤是否过于严格

### 修复3：Pod启动问题解决

**目标**：解决 ContainerCreating 状态卡住的问题

**可能原因**：
1. 镜像拉取问题
2. 资源不足
3. 网络问题

## 📊 网络连接修复实现

### Step 1: 修改局部图网络处理

```go
// 在 alastor.go 中，修改网络连接处理逻辑
func (p *AlastorParser) handleNetworkConnection(comm *NetworkCommunication, graph *gographviz.Graph) {
    // 只处理通过 Socket 的连接，不直接创建 NetPeer
    if sourceSocket := p.findSocketForPort(comm.SourcePort); sourceSocket != "" {
        // 从 Socket 连接到外部，让全局图处理具体的路由
        // 不在局部图中创建 NetPeer 节点
    }
}
```

### Step 2: 修改全局图连接逻辑

```go
// 在 local_graph_based_builder.go 中
func (lgb *LocalGraphBasedBuilder) buildNetworkConnections(globalGraph *gographviz.Graph) {
    // 1. 解析 nettaint.log 获取真实网络连接
    // 2. 建立 Socket → Gateway → 外部服务的连接
    // 3. 移除容器内部的 NetPeer 节点
}
```

### Step 3: 系统调用捕获验证

```bash
# 手动测试 sas6 攻击
curl -d @requests/sas6.json -H "Content-Type: application/json" -X POST 'http://localhost:31112/function/product-purchase-authorize-cc'

# 检查是否有文件读取系统调用
grep -r "open.*passwd" logs/*/processed_logs/
grep -r "readFileSync" logs/*/app_logs/
```

## 🔧 立即可执行的修复

### 1. 临时解决Pod启动问题

```bash
# 检查镜像拉取状态
kubectl describe pod product-purchase-6c4947b4c9-rh6bm -n openfaas-fn

# 强制重启相关服务
kubectl delete pods -n openfaas-fn --all
kubectl delete deployment -n openfaas-fn --all
```

### 2. 验证sas6攻击

```bash
# 直接测试单个函数
curl -d '{"id":1,"user":"alice","creditCard":"1234-5678-9","malicious":"sas6","path":"/etc/passwd"}' \
  -H "Content-Type: application/json" \
  -X POST 'http://localhost:31112/function/product-purchase-authorize-cc'
```

### 3. 检查系统调用捕获

```bash
# 检查 strace 是否正常工作
ps aux | grep strace
ls -la /tmp/alastor-*
```

## 📈 预期修复效果

### 修复前的网络图：
```dot
// 错误的表示
"Container" -> "NetPeer##127.0.0.1:35274"
"Container" -> "NetPeer##attackserver:8888"  // 直接连接，错误
```

### 修复后的网络图：
```dot
// 正确的表示
"Container" -> "Socket##0.0.0.0:3000"
"Socket##0.0.0.0:3000" -> "Gateway##gateway:8080"
"Gateway##gateway:8080" -> "NetPeer##attackserver:8888"
```

### 系统调用捕获增强：
- ✅ 确保 `open("/etc/passwd")` 被捕获
- ✅ 确保 `read()` 系统调用被记录
- ✅ 确保攻击行为在图中可见

这个修复方案将显著提升 Alastor 的网络连接表示准确性和攻击检测能力。
