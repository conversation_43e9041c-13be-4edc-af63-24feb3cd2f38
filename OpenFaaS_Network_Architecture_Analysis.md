# OpenFaaS网络架构深度分析

## 概述

通过深入分析OpenFaaS函数的源代码、配置文件和系统调用日志，我发现了网络连接的真实路径与我之前的假设存在重大差异。**并非所有网络连接都通过Socket 3000**，实际的网络架构更加复杂和精确。

## 核心发现

### 1. **端口3000的真实作用**

**端口3000只用于接收来自OpenFaaS Gateway的HTTP请求**，而不是所有网络连接的中转站。

#### 证据：
- **Node.js函数代码**：`const port = process.env.http_port || 3000; app.listen(port, ...)`
- **系统调用日志**：
  ```
  socket(AF_INET6, SOCK_STREAM|SOCK_CLOEXEC|SOCK_NONBLOCK, IPPROTO_IP) = 18
  bind(18, {sa_family=AF_INET6, sin6_port=htons(3000), ...}) = 0
  listen(18, 511) = 0
  accept4(18, NULL, NULL, SOCK_CLOEXEC|SOCK_NONBLOCK) = 19
  ```

### 2. **网络连接的三种路径**

#### 路径1：函数间通信（通过Gateway）
- **路径**：`函数A Socket 3000 ← Gateway → 函数B Socket 3000`
- **特征**：使用`request-promise`库，经过mitmproxy代理
- **示例**：product-purchase调用product-purchase-get-price
- **代码证据**：
  ```javascript
  const request = require('request-promise')
  request(functions.getRequestObject(getPriceData, constants.URL_GETPRICE))
  ```

#### 路径2：直接外部服务连接（不经过Socket 3000）
- **路径**：`函数进程 → 外部服务`
- **特征**：直接的socket连接，不经过mitmproxy代理
- **示例**：
  - **数据库连接**：`connect(3, {sin_port=htons(3306), sin_addr=inet_addr("*************")})`
  - **DNS查询**：`sendto(3, ..., {sin_port=htons(53), sin_addr=inet_addr("*************")})`

#### 路径3：攻击服务器连接（经过代理）
- **路径**：`函数进程 → mitmproxy → attackserver`
- **特征**：使用支持代理的HTTP库
- **示例**：下载恶意脚本
- **代码证据**：
  ```javascript
  // Alastor版本使用request-promise（支持代理）
  let response_body = await request(url);
  ```

### 3. **mitmproxy代理的作用范围**

**mitmproxy只捕获使用代理感知HTTP库的请求**，不是所有网络流量。

#### 被代理的连接：
- 函数间HTTP调用（使用`request-promise`）
- 攻击服务器HTTP请求（Alastor版本）

#### 不被代理的连接：
- 数据库连接（MySQL协议，直接socket）
- DNS查询（UDP，系统级调用）
- 原生socket连接

## 技术实现细节

### 1. **OpenFaaS Watchdog架构**

```
┌─────────────────────────────────────────────────────────┐
│                    容器内部                              │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐  │
│  │  Watchdog   │    │ mitmproxy   │    │ Node.js函数  │  │
│  │   :8080     │    │   :18080    │    │   :3000     │  │
│  └─────────────┘    └─────────────┘    └─────────────┘  │
│         │                   │                   │       │
│         │ HTTP Forward      │ Proxy             │       │
│         └───────────────────┼───────────────────┘       │
│                             │                           │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              外部连接（不经过代理）                   │ │
│  │  • MySQL: 直接连接 cc-db:3306                      │ │
│  │  • DNS: 直接查询 *************:53                  │ │
│  │  • 其他TCP/UDP连接                                 │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 2. **配置分析**

#### Watchdog配置：
- **TCPPort**: 8080（接收Gateway请求）
- **UpstreamURL**: `http://127.0.0.1:3000`（转发到Node.js函数）

#### 代理环境变量：
```bash
http_proxy=http://127.0.0.1:18080
https_proxy=http://127.0.0.1:18080
NODE_TLS_REJECT_UNAUTHORIZED=0
```

### 3. **系统调用证据**

#### Node.js函数启动：
```
socket(AF_INET6, SOCK_STREAM|SOCK_CLOEXEC|SOCK_NONBLOCK, IPPROTO_IP) = 18
bind(18, {sa_family=AF_INET6, sin6_port=htons(3000), ...}) = 0
listen(18, 511) = 0
```

#### 数据库连接（mysqldump进程）：
```
socket(AF_INET, SOCK_STREAM, IPPROTO_TCP) = 3
connect(3, {sa_family=AF_INET, sin_port=htons(3306), sin_addr=inet_addr("*************")}) = -1 EINPROGRESS
```

#### HTTP代理连接：
```
socket(AF_INET, SOCK_STREAM|SOCK_CLOEXEC|SOCK_NONBLOCK, IPPROTO_IP) = 20
connect(20, {sa_family=AF_INET, sin_port=htons(18080), sin_addr=inet_addr("127.0.0.1")}) = -1 EINPROGRESS
```

## 修正后的全局图连接策略

### 1. **函数间通信**
- **连接方式**：`源函数Socket 3000 → Gateway → 目标函数Socket 3000`
- **标识**：通过nettaint.log中的HTTP请求识别

### 2. **外部服务连接**
- **连接方式**：`函数进程 → 外部服务`
- **标识**：通过strace中的connect/sendto/recvfrom系统调用识别
- **不经过Socket 3000**

### 3. **DNS查询**
- **连接方式**：`函数进程 → DNS服务器`
- **特征**：UDP协议，端口53
- **不经过Socket 3000**

## 结论

**Socket 3000只是OpenFaaS函数的HTTP服务端口，用于接收来自Gateway的请求。大部分外部服务连接（数据库、DNS、直接TCP连接）都是由函数进程直接发起的，不经过Socket 3000。**

这个发现对Alastor的溯源图生成有重要影响：
1. **函数间通信**：应该通过Socket 3000连接
2. **外部服务连接**：应该直接从函数进程连接到外部服务
3. **DNS查询**：应该直接从函数进程连接到DNS服务器

这样的表示更准确地反映了实际的网络流量路径，避免了错误的抽象。
