{"name": "function", "version": "1.0.0", "description": "", "main": "handler.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"global-agent": "^3.0.0", "request-promise": "^4.2.4", "fs": "0.0.1-security", "body-parser": "^1.18.2", "express": "^4.16.2", "mysql2": "^3.12.0", "node-serialize": "^0.0.4", "child_process": "^1.0.2"}}