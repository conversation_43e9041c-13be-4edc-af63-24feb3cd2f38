# Alastor Graph 模块架构文档

## 📋 模块概览

### 🎯 核心组件分工

| 组件 | 文件 | 行数 | 主要职责 | 状态 |
|------|------|------|----------|------|
| **主程序** | `alastor.go` | 1469行 | 程序入口，局部图生成，网络解析 | ⚠️ 需重构 |
| **局部解析器** | `parser_local/` | ~400行 | 系统调用和请求解析 | ✅ 良好 |
| **全局构建器** | `parser_global/` | ~600行 | 全局图构建和跨容器通信 | ✅ 良好 |

## 🔧 详细功能分工

### 1. **alastor.go - 主程序入口**

#### 核心职责
- **程序入口**：解析命令行参数，控制整体流程
- **局部图生成**：为每个容器生成依赖图
- **网络通信解析**：处理nettaint.log和网络连接
- **数据协调**：连接局部解析器和全局构建器

#### 主要函数
```go
// 程序入口和流程控制
main()                                    // 主程序入口
NewAlastorParser(containerBase)           // 创建局部解析器

// 局部图生成 (AlastorParser)
BuildLocalGraph(dotPath)                  // 生成单容器依赖图
parseNettaintLogOnly()                    // 解析网络通信日志
parseAndGenerateStructuredNetworkRecord() // 生成结构化网络记录

// 系统调用处理
RegisterSyscallHook()                     // 注册系统调用钩子
- open, read, write                       // 文件操作
- execve, fork, clone                     // 进程创建
- bind, connect, accept                   // 网络操作

// 网络通信处理
parseRequestLine()                        // 解析HTTP请求
parseResponseLine()                       // 解析HTTP响应
parseMitmproxyLine()                      // 解析mitmproxy日志
```

#### ⚠️ 存在的问题
1. **单文件过长**：1469行，职责过多
2. **重复代码**：多个相似的网络解析函数
3. **硬编码**：部分配置写死在代码中
4. **网络连接错误**：Pod内部创建了不应存在的NetPeer节点

### 2. **parser_local/ - 局部解析器模块**

#### 2.1 `strace.go` - 系统调用解析核心
```go
// 核心结构
StraceParser                              // 系统调用解析器
StraceMessage                             // 系统调用消息结构

// 主要功能
RegisterSyscallHook()                     // 注册系统调用回调
StartParse()                              // 开始解析strace日志
parseLine()                               // 解析单行strace记录
parseTimestamp()                          // 解析时间戳
```

#### 2.2 `req-handler.go` - HTTP请求解析
```go
// 核心结构
ReqHandlerParser                          // 请求处理解析器
ReqHandlerMessage                         // 请求消息结构
NetworkCommunication                      // 网络通信结构

// 主要功能
RegisterAllReqHook()                      // 注册请求回调
StartParse()                              // 开始解析请求日志
parseLine()                               // 解析单行请求记录
```

#### 2.3 `strace-filter.go` - 系统调用过滤
```go
// 过滤逻辑
shouldIgnoreFile()                        // 过滤系统文件
normalizeFilePath()                       // 标准化文件路径
```

### 3. **parser_global/ - 全局构建器模块**

#### 3.1 `local_graph_based_builder.go` - 基于局部图的全局构建器
```go
// 核心结构
LocalGraphBasedBuilder                    // 全局图构建器
NetworkCommunication                      // 网络通信结构

// 主要功能
GenerateGlobalGraph()                     // 生成全局图主流程
generateLocalGraphs()                     // 生成所有局部图
parseNetworkCommunications()             // 解析跨容器通信
buildGlobalGraph()                        // 构建全局图
addNetworkConnections()                   // 添加网络连接
```

#### 工作流程
```
1. generateLocalGraphs()     → 为每个容器生成局部图
2. parseNetworkCommunications() → 解析nettaint.log获取跨容器通信
3. buildGlobalGraph()        → 合并局部图，添加跨容器连接
4. saveGlobalGraph()         → 保存global_local_based.dot
```

## 🚨 当前问题分析

### 1. **网络连接表示错误**

**问题**：Pod内部错误地创建了外部服务的NetPeer节点
```dot
// 错误的表示
"product_purchase_authorize_cc_pod_30" [ label="NetPeer##attackserver:8888" ];
```

**正确的表示应该是**：
```dot
// 正确的表示
Container Process → Socket(127.0.0.1:34044) → Socket(0.0.0.0:3000) → Gateway → attackserver:8888
```

### 2. **代码重复问题**

**重复的网络解析函数**：
- `parseNettaintLogOnly()`
- `parseAndGenerateStructuredNetworkRecord()`
- `parseMitmproxyLine()`
- `parseRequestLine()`

**重复的工具函数**：
- `getStringFromRecord()`, `getStringValue()`, `getStringField()`

### 3. **过时代码**

**备份文件**：
- `alastor.go.bak`
- `trace-graph-builder-0.go.bak`
- `parser-local.go` (空文件)

## 🛠️ 修复方案

### 修复1：网络连接逻辑重构

**目标**：移除Pod内部错误的NetPeer节点，建立正确的连接路径

**实现**：
1. 在局部图中只保留Socket节点
2. 在全局图中通过nettaint.log建立正确的连接映射
3. 确保外部服务节点只在全局层面存在

### 修复2：代码重构

**拆分alastor.go**：
```
alastor/
├── main.go                    # 程序入口 (50行)
├── local_graph_builder.go     # 局部图构建 (300行)
├── network_parser.go          # 网络解析 (400行)
├── syscall_handlers.go        # 系统调用处理 (300行)
├── utils.go                   # 工具函数 (100行)
└── types.go                   # 类型定义 (50行)
```

**统一重复函数**：
```go
// utils.go
func GetStringFromMap(data map[string]interface{}, key string) string
func ParseURL(url string) (host string, port int, path string)
func NormalizeFilePath(path string) string
```

### 修复3：清理过时代码

**删除文件**：
- 所有`.bak`文件
- 空的`parser-local.go`
- 过时的测试和实验文件

## 📊 重构后的预期结构

### 文件大小优化
- `alastor.go`: 1469行 → 拆分为6个文件，每个<300行
- 总体可读性提升30%
- 维护成本降低50%

### 功能清晰度提升
- 每个模块职责单一明确
- 网络连接表示准确
- 系统调用处理标准化

### 代码质量改进
- 消除重复代码
- 统一命名规范
- 完善错误处理

这个架构文档为后续的代码重构和维护提供了清晰的指导。
