/*
Package parser_global 实现基于局部图的全局溯源图生成器

思路二：局部溯源图 + 跨容器通信
- 复用parser_local的逻辑生成每个容器的局部图
- 移除椭圆形容器节点和contains边
- 使用pod方块内的socket节点进行跨容器通信
- Socket(3000) -> Gateway -> Socket(3000) 的连接模式
*/
package parser_global

import (
	parser_local "alastor/parser_local"
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/awalterschulze/gographviz"
)

// NetworkCommunication 网络通信记录（与现有结构体保持一致）
type NetworkCommunication struct {
	Source        string
	SourceIP      string
	SourcePort    string
	SourcePod     string // 源Pod名称
	DestIP        string
	DestPort      int
	Target        string
	Method        string
	Path          string
	CommType      string // "function_call", "network_communication"
	Timestamp     string
	ContainerName string // 容器名称
	ProcessPID    int    // 进程ID（如果可获取）
	EventType     string // 事件类型（如"response"）
	DestPod       string // 目标Pod名称
}

// LocalGraphBasedBuilder 基于局部图的全局溯源图构建器
type LocalGraphBasedBuilder struct {
	containerDirs        []string
	outputDir            string
	localParsers         map[string]*parser_local.StraceParser
	networkComms         []*NetworkCommunication
	containerGraphs      map[string]*gographviz.Graph
	containerSocketNodes map[string]string // 容器Socket节点映射
	connectionCounter    int               // 连接计数器
}

// NewLocalGraphBasedBuilder 创建新的基于局部图的构建器
func NewLocalGraphBasedBuilder(containerDirs []string, outputDir string) *LocalGraphBasedBuilder {
	return &LocalGraphBasedBuilder{
		containerDirs:        containerDirs,
		outputDir:            outputDir,
		localParsers:         make(map[string]*parser_local.StraceParser),
		networkComms:         make([]*NetworkCommunication, 0),
		containerGraphs:      make(map[string]*gographviz.Graph),
		containerSocketNodes: make(map[string]string),
		connectionCounter:    1, // 初始化计数器
	}
}

// GenerateGlobalGraph 生成基于局部图的全局溯源图
func (lgb *LocalGraphBasedBuilder) GenerateGlobalGraph() error {
	fmt.Println("=== 基于局部图的全局溯源图生成器 ===")

	// 1. 生成所有容器的局部图
	if err := lgb.generateLocalGraphs(); err != nil {
		return fmt.Errorf("生成局部图失败: %v", err)
	}

	// 2. 解析跨容器网络通信
	if err := lgb.parseNetworkCommunications(); err != nil {
		return fmt.Errorf("解析网络通信失败: %v", err)
	}

	// 3. 解析系统调用级别的外部连接
	if err := lgb.parseExternalSystemCalls(); err != nil {
		return fmt.Errorf("解析外部系统调用失败: %v", err)
	}

	// 4. 构建全局图
	globalGraph, err := lgb.buildGlobalGraph()
	if err != nil {
		return fmt.Errorf("构建全局图失败: %v", err)
	}

	// 4. 保存全局图
	outputPath := filepath.Join(lgb.outputDir, "global_local_based.dot")
	return lgb.saveGlobalGraph(globalGraph, outputPath)
}

// generateLocalGraphs 生成所有容器的局部图
func (lgb *LocalGraphBasedBuilder) generateLocalGraphs() error {
	fmt.Println("生成所有容器的局部图...")

	for _, containerDir := range lgb.containerDirs {
		containerName := filepath.Base(containerDir)
		fmt.Printf("生成容器局部图: %s\n", containerName)

		// 创建局部解析器
		parser := parser_local.NewStraceParser()
		lgb.localParsers[containerName] = parser

		// 生成局部图
		localGraph, err := lgb.generateSingleLocalGraph(parser, containerName)
		if err != nil {
			fmt.Printf("警告: 生成容器 %s 的局部图失败: %v\n", containerName, err)
			continue
		}

		lgb.containerGraphs[containerName] = localGraph
		fmt.Printf("✅ 容器 %s 局部图生成完成\n", containerName)
	}

	return nil
}

// generateSingleLocalGraph 生成单个容器的局部图
func (lgb *LocalGraphBasedBuilder) generateSingleLocalGraph(parser *parser_local.StraceParser, containerName string) (*gographviz.Graph, error) {
	// 初始化图
	graphAst, _ := gographviz.Parse([]byte(`digraph G{}`))
	graph := gographviz.NewGraph()
	err := gographviz.Analyse(graphAst, graph)
	if err != nil {
		return nil, fmt.Errorf("初始化图失败: %v", err)
	}

	// 解析系统调用日志，构建局部图
	// 这里复用parser_local的逻辑
	err = lgb.parseLocalSystemCalls(parser, graph, containerName)
	if err != nil {
		return nil, fmt.Errorf("解析系统调用失败: %v", err)
	}

	return graph, nil
}

// parseLocalSystemCalls 解析局部系统调用（调用现有的AlastorParser逻辑）
func (lgb *LocalGraphBasedBuilder) parseLocalSystemCalls(parser *parser_local.StraceParser, graph *gographviz.Graph, containerName string) error {
	// 找到对应的容器目录
	containerDir := ""
	for _, dir := range lgb.containerDirs {
		if strings.Contains(dir, containerName) {
			containerDir = dir
			break
		}
	}

	if containerDir == "" {
		return fmt.Errorf("未找到容器目录: %s", containerName)
	}

	fmt.Printf("为容器 %s 生成真正的局部溯源图...\n", containerName)

	// 直接读取已生成的局部图DOT文件
	localDotPath := filepath.Join(lgb.outputDir, containerName+".dot")
	if _, err := os.Stat(localDotPath); os.IsNotExist(err) {
		return fmt.Errorf("局部图文件不存在: %s", localDotPath)
	}

	// 读取并解析现有的DOT文件
	dotContent, err := os.ReadFile(localDotPath)
	if err != nil {
		return fmt.Errorf("读取局部图文件失败: %v", err)
	}

	// 解析DOT内容到临时图对象
	tempGraph := gographviz.NewGraph()
	graphAst, err := gographviz.Parse(dotContent)
	if err != nil {
		return fmt.Errorf("解析DOT文件失败: %v", err)
	}

	err = gographviz.Analyse(graphAst, tempGraph)
	if err != nil {
		return fmt.Errorf("分析DOT图失败: %v", err)
	}

	// 将临时图的内容复制到传入的图对象中
	for _, node := range tempGraph.Nodes.Nodes {
		nodeAttrs := make(map[string]string)
		for k, v := range node.Attrs {
			nodeAttrs[string(k)] = string(v)
		}
		graph.AddNode("G", node.Name, nodeAttrs)
	}

	for _, edge := range tempGraph.Edges.Edges {
		edgeAttrs := make(map[string]string)
		for k, v := range edge.Attrs {
			edgeAttrs[string(k)] = string(v)
		}
		graph.AddEdge(edge.Src, edge.Dst, true, edgeAttrs)
	}

	fmt.Printf("✅ 成功加载容器 %s 的局部图 (节点数: %d, 边数: %d)\n",
		containerName, len(tempGraph.Nodes.Nodes), len(tempGraph.Edges.Edges))
	return nil
}

// parseNetworkCommunications 解析跨容器网络通信
func (lgb *LocalGraphBasedBuilder) parseNetworkCommunications() error {
	fmt.Println("解析跨容器网络通信...")

	for _, containerDir := range lgb.containerDirs {
		containerName := filepath.Base(containerDir)

		// 解析nettaint.log
		nettaintPath := filepath.Join(containerDir, "nettaint.log")
		if _, err := os.Stat(nettaintPath); os.IsNotExist(err) {
			fmt.Printf("容器 %s 没有nettaint.log文件\n", containerName)
			continue
		}

		comms, err := lgb.parseNettaintLog(nettaintPath, containerName)
		if err != nil {
			fmt.Printf("警告: 解析容器 %s 的网络日志失败: %v\n", containerName, err)
			continue
		}

		lgb.networkComms = append(lgb.networkComms, comms...)
		fmt.Printf("容器 %s: 解析到 %d 个网络通信记录\n", containerName, len(comms))
	}

	fmt.Printf("总共解析到 %d 个跨容器网络通信记录\n", len(lgb.networkComms))
	return nil
}

// parseNettaintLog 解析单个容器的nettaint.log
func (lgb *LocalGraphBasedBuilder) parseNettaintLog(nettaintPath, containerName string) ([]*NetworkCommunication, error) {
	file, err := os.Open(nettaintPath)
	if err != nil {
		return nil, fmt.Errorf("打开nettaint.log失败: %v", err)
	}
	defer file.Close()

	comms := make([]*NetworkCommunication, 0)

	// 简化的解析逻辑，提取HTTP请求信息
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// 跳过注释和空行
		if strings.HasPrefix(line, "#") || line == "" {
			continue
		}

		// 解析HTTP请求行格式：127.0.0.1:35272: GET http://attackserver.openfaas-fn.svc.cluster.local:8888/sqldump.sh
		if strings.Contains(line, ": GET ") || strings.Contains(line, ": POST ") {
			comm := lgb.parseHTTPRequestLine(line, containerName)
			if comm != nil {
				comms = append(comms, comm)
			}
		}
	}

	return comms, scanner.Err()
}

// parseHTTPRequestLine 解析HTTP请求行
func (lgb *LocalGraphBasedBuilder) parseHTTPRequestLine(line, containerName string) *NetworkCommunication {
	// 格式：127.0.0.1:35272: GET http://attackserver.openfaas-fn.svc.cluster.local:8888/sqldump.sh
	parts := strings.Split(line, ": ")
	if len(parts) < 2 {
		return nil
	}

	sourceAddr := parts[0]
	requestPart := parts[1]

	// 解析源地址
	sourceIP := ""
	sourcePort := ""
	if addrParts := strings.Split(sourceAddr, ":"); len(addrParts) == 2 {
		sourceIP = addrParts[0]
		sourcePort = addrParts[1]
	}

	// 解析HTTP请求
	requestParts := strings.Fields(requestPart)
	if len(requestParts) < 2 {
		return nil
	}

	method := requestParts[0]
	url := requestParts[1]

	// 解析URL
	destIP, destPort, path := lgb.parseURL(url)
	if destIP == "" {
		return nil
	}

	// 确定通信类型
	commType := "network_communication"
	if strings.Contains(destIP, "gateway") && strings.Contains(path, "/function/") {
		commType = "function_call"
	}

	return &NetworkCommunication{
		Source:        sourceIP,
		SourceIP:      sourceIP,
		SourcePort:    sourcePort,
		SourcePod:     containerName,
		DestIP:        destIP,
		DestPort:      destPort,
		Target:        destIP,
		Method:        method,
		Path:          path,
		CommType:      commType,
		ContainerName: containerName,
	}
}

// parseURL 解析URL获取目标信息
func (lgb *LocalGraphBasedBuilder) parseURL(url string) (string, int, string) {
	// 移除http://前缀
	if strings.HasPrefix(url, "http://") {
		url = url[7:]
	}

	// 分离主机和路径
	parts := strings.SplitN(url, "/", 2)
	hostPort := parts[0]
	path := "/"
	if len(parts) > 1 {
		path = "/" + parts[1]
	}

	// 分离主机和端口
	host := hostPort
	port := 80 // 默认端口
	if colonIndex := strings.LastIndex(hostPort, ":"); colonIndex != -1 {
		host = hostPort[:colonIndex]
		if portStr := hostPort[colonIndex+1:]; portStr != "" {
			if p, err := strconv.Atoi(portStr); err == nil {
				port = p
			}
		}
	}

	return host, port, path
}

// parseExternalSystemCalls 解析系统调用级别的外部连接
func (lgb *LocalGraphBasedBuilder) parseExternalSystemCalls() error {
	fmt.Println("解析系统调用级别的外部连接...")

	for _, containerDir := range lgb.containerDirs {
		containerName := filepath.Base(containerDir)

		// 查找所有strace文件
		straceFiles, err := filepath.Glob(filepath.Join(containerDir, "request.alastor.*"))
		if err != nil {
			continue
		}

		for _, straceFile := range straceFiles {
			// 跳过.log文件
			if strings.HasSuffix(straceFile, ".log") {
				continue
			}

			if err := lgb.parseStraceFileForExternalConnections(straceFile, containerName); err != nil {
				fmt.Printf("解析strace文件失败 %s: %v\n", straceFile, err)
				continue
			}
		}
	}

	return nil
}

// parseStraceFileForExternalConnections 解析单个strace文件中的外部连接
func (lgb *LocalGraphBasedBuilder) parseStraceFileForExternalConnections(straceFile, containerName string) error {
	file, err := os.Open(straceFile)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// 解析connect系统调用
		if strings.Contains(line, "connect(") && strings.Contains(line, "10.152.183.") {
			if err := lgb.parseConnectCall(line, containerName); err != nil {
				continue // 解析失败，继续下一行
			}
		}

		// 解析sendto/recvfrom系统调用
		if (strings.Contains(line, "sendto(") || strings.Contains(line, "recvfrom(")) &&
			strings.Contains(line, "10.152.183.") {
			if err := lgb.parseDNSCall(line, containerName); err != nil {
				continue // 解析失败，继续下一行
			}
		}
	}

	return scanner.Err()
}

// parseConnectCall 解析connect系统调用
func (lgb *LocalGraphBasedBuilder) parseConnectCall(line, containerName string) error {
	// 提取IP和端口
	// 例如: connect(3, {sa_family=AF_INET, sin_port=htons(3306), sin_addr=inet_addr("*************")}, 16)

	// 提取IP地址
	ipStart := strings.Index(line, "inet_addr(\"")
	if ipStart == -1 {
		return fmt.Errorf("未找到IP地址")
	}
	ipStart += len("inet_addr(\"")
	ipEnd := strings.Index(line[ipStart:], "\"")
	if ipEnd == -1 {
		return fmt.Errorf("IP地址格式错误")
	}
	ip := line[ipStart : ipStart+ipEnd]

	// 提取端口
	portStart := strings.Index(line, "htons(")
	if portStart == -1 {
		return fmt.Errorf("未找到端口")
	}
	portStart += len("htons(")
	portEnd := strings.Index(line[portStart:], ")")
	if portEnd == -1 {
		return fmt.Errorf("端口格式错误")
	}
	portStr := line[portStart : portStart+portEnd]
	port, err := strconv.Atoi(portStr)
	if err != nil {
		return fmt.Errorf("端口转换失败: %v", err)
	}

	// 创建外部连接记录
	// 注意：在OpenFaaS中，所有网络流量都经过mitmproxy代理，因此使用Socket 3000
	comm := &NetworkCommunication{
		SourcePod:  containerName,
		SourceIP:   "127.0.0.1",
		SourcePort: "3000", // 所有网络流量都经过Socket 3000
		DestIP:     ip,
		DestPort:   port,
		Method:     "CONNECT",
		Path:       fmt.Sprintf(":%d", port),
	}

	lgb.networkComms = append(lgb.networkComms, comm)
	fmt.Printf("添加外部连接: %s -> %s:%d (connect系统调用)\n", containerName, ip, port)

	return nil
}

// parseDNSCall 解析DNS系统调用
func (lgb *LocalGraphBasedBuilder) parseDNSCall(line, containerName string) error {
	// 提取IP地址
	ipStart := strings.Index(line, "inet_addr(\"")
	if ipStart == -1 {
		return fmt.Errorf("未找到IP地址")
	}
	ipStart += len("inet_addr(\"")
	ipEnd := strings.Index(line[ipStart:], "\"")
	if ipEnd == -1 {
		return fmt.Errorf("IP地址格式错误")
	}
	ip := line[ipStart : ipStart+ipEnd]

	// DNS通常是53端口
	port := 53

	// 创建外部连接记录
	// 注意：在OpenFaaS中，所有网络流量都经过mitmproxy代理，因此使用Socket 3000
	comm := &NetworkCommunication{
		SourcePod:  containerName,
		SourceIP:   "127.0.0.1",
		SourcePort: "3000", // 所有网络流量都经过Socket 3000
		DestIP:     ip,
		DestPort:   port,
		Method:     "DNS",
		Path:       "/dns",
	}

	lgb.networkComms = append(lgb.networkComms, comm)
	fmt.Printf("添加外部连接: %s -> %s:%d (DNS系统调用)\n", containerName, ip, port)

	return nil
}

// buildGlobalGraph 构建全局图
func (lgb *LocalGraphBasedBuilder) buildGlobalGraph() (*gographviz.Graph, error) {
	fmt.Println("构建全局图...")

	// 初始化全局图
	graphAst, _ := gographviz.Parse([]byte(`digraph G{}`))
	globalGraph := gographviz.NewGraph()
	err := gographviz.Analyse(graphAst, globalGraph)
	if err != nil {
		return nil, fmt.Errorf("初始化全局图失败: %v", err)
	}

	// 1. 添加全局Gateway节点
	gatewayNode := lgb.addQuotation("Gateway##gateway.openfaas.svc.cluster.local:8080")
	globalGraph.AddNode("G", gatewayNode, map[string]string{
		"shape": lgb.addQuotation("hexagon"),
		"color": lgb.addQuotation("orange"),
		"label": lgb.addQuotation("gateway.openfaas.svc.cluster.local:8080"),
	})

	// 2. 添加所有局部图作为子图
	for containerName, localGraph := range lgb.containerGraphs {
		lgb.addLocalGraphAsSubgraph(globalGraph, localGraph, containerName)
	}

	// 3. 添加跨容器网络连接
	lgb.addCrossContainerConnections(globalGraph)

	return globalGraph, nil
}

// addLocalGraphAsSubgraph 将局部图添加为子图（使用唯一节点名+相同label策略）
func (lgb *LocalGraphBasedBuilder) addLocalGraphAsSubgraph(globalGraph, localGraph *gographviz.Graph, containerName string) {
	// 创建容器子图 - 替换连字符为下划线避免DOT语法问题
	safeName := strings.ReplaceAll(containerName, "-", "_")
	clusterName := fmt.Sprintf("cluster_%s", safeName)
	globalGraph.AddSubGraph("G", clusterName, map[string]string{
		"label": lgb.addQuotation(containerName),
	})

	// 为每个节点创建唯一名称，但保持原始label
	nodeCounter := 1
	nodeMapping := make(map[string]string) // 原始名称 -> 唯一名称的映射

	// 添加局部图的所有节点到子图
	for _, node := range localGraph.Nodes.Nodes {
		originalNodeName := strings.Trim(node.Name, "\"")

		// 跳过Gateway相关的NetPeer节点，因为我们有全局Gateway节点
		if strings.Contains(originalNodeName, "NetPeer##gateway") {
			fmt.Printf("跳过容器内的Gateway NetPeer节点: %s\n", originalNodeName)
			continue
		}

		// 创建唯一的节点名称
		uniqueNodeName := fmt.Sprintf("\"%s_%d\"", safeName, nodeCounter)
		nodeCounter++

		// 保存映射关系
		nodeMapping[node.Name] = uniqueNodeName

		// 转换属性类型，保持原始label
		nodeAttrs := make(map[string]string)
		for k, v := range node.Attrs {
			nodeAttrs[string(k)] = string(v)
		}

		// 如果没有label属性，使用原始节点名作为label
		if _, hasLabel := nodeAttrs["label"]; !hasLabel {
			nodeAttrs["label"] = lgb.addQuotation(originalNodeName)
		}

		// 添加到子图
		globalGraph.AddNode(clusterName, uniqueNodeName, nodeAttrs)

		// 记录Socket节点用于跨容器连接
		if originalNodeName == "Socket##0.0.0.0:3000" {
			lgb.containerSocketNodes[containerName] = uniqueNodeName
		}
	}

	// 添加局部图的所有边到子图（需要更新节点名称）
	for _, edge := range localGraph.Edges.Edges {
		srcNodeName := nodeMapping[edge.Src]
		dstNodeName := nodeMapping[edge.Dst]

		if srcNodeName == "" || dstNodeName == "" {
			fmt.Printf("警告: 找不到边的节点映射 %s -> %s\n", edge.Src, edge.Dst)
			continue
		}

		// 转换边属性
		edgeAttrs := make(map[string]string)
		for k, v := range edge.Attrs {
			edgeAttrs[string(k)] = string(v)
		}

		// 添加边到全局图
		globalGraph.AddEdge(srcNodeName, dstNodeName, true, edgeAttrs)
	}

}

// addCrossContainerConnections 添加跨容器连接
func (lgb *LocalGraphBasedBuilder) addCrossContainerConnections(globalGraph *gographviz.Graph) {
	fmt.Println("添加跨容器网络连接...")

	for _, comm := range lgb.networkComms {
		// 跳过没有HTTP信息的记录
		if comm.Method == "" || comm.Path == "" {
			continue
		}

		// 构建连接标签
		label := fmt.Sprintf("%s %s", comm.Method, comm.Path)

		// 确定连接样式
		var color, style string
		if strings.Contains(comm.DestIP, "attackserver") {
			color = "red"
			style = "solid"
		} else {
			color = "blue"
			style = "solid"
		}

		// 查找源容器的Socket节点
		sourceSocketNode := lgb.findSocketNodeInContainer(comm.SourcePod, globalGraph)
		if sourceSocketNode == "" {
			fmt.Printf("警告: 未找到容器 %s 的Socket节点\n", comm.SourcePod)
			continue
		}

		// 判断是否为容器间通信（需要Gateway）还是外部通信
		if strings.Contains(comm.DestIP, "gateway") && strings.Contains(comm.Path, "/function/") {
			// 容器间函数调用：Socket -> Gateway -> Socket
			lgb.addContainerToContainerConnection(globalGraph, sourceSocketNode, comm, label, color, style)
		} else {
			// 外部通信：所有外部连接都通过Socket 3000
			// 这是因为在OpenFaaS中，所有网络流量都经过mitmproxy代理
			lgb.addExternalConnection(globalGraph, sourceSocketNode, comm, label, color, style)
		}
	}
}

// addContainerToContainerConnection 添加容器间连接：Socket -> Gateway -> Socket
func (lgb *LocalGraphBasedBuilder) addContainerToContainerConnection(globalGraph *gographviz.Graph, sourceSocketNode string, comm *NetworkCommunication, label, color, style string) {
	// 从路径中提取目标函数名
	// 例如：/function/product-purchase-get-price -> product-purchase-get-price
	targetFunctionName := ""
	if strings.HasPrefix(comm.Path, "/function/") {
		targetFunctionName = comm.Path[10:] // 去掉 "/function/" 前缀
	}

	if targetFunctionName == "" {
		fmt.Printf("无法从路径 %s 提取目标函数名\n", comm.Path)
		return
	}

	// 查找目标容器的Socket节点
	var targetSocketNode string
	var targetContainerName string
	for _, containerDir := range lgb.containerDirs {
		containerName := filepath.Base(containerDir)
		if strings.Contains(containerName, targetFunctionName) {
			targetSocketNode = lgb.findSocketNodeInContainer(containerName, globalGraph)
			targetContainerName = containerName
			break
		}
	}

	if targetSocketNode == "" {
		fmt.Printf("未找到目标函数 %s 对应的Socket节点\n", targetFunctionName)
		return
	}

	// Gateway节点已经在buildGlobalGraph中创建，直接引用
	gatewayNode := lgb.addQuotation("Gateway##gateway.openfaas.svc.cluster.local:8080")

	// // 生成唯一的连接ID，用于标识Gateway前后两条边的对应关系
	// connectionID := fmt.Sprintf("conn_%s_%s",
	// 	strings.ReplaceAll(comm.SourcePod, "-", "_"),
	// 	strings.ReplaceAll(targetContainerName, "-", "_"))
	// 生成唯一的连接ID，使用自增计数器
	connectionID := fmt.Sprintf("%d", lgb.connectionCounter)
	lgb.connectionCounter++ // 自增计数器

	// 添加连接：Source Socket -> Gateway -> Target Socket
	globalGraph.AddEdge(sourceSocketNode, gatewayNode, true, map[string]string{
		"label": lgb.addQuotation(fmt.Sprintf("%s <%s>", label, connectionID)),
		"color": color,
		"style": style,
	})

	globalGraph.AddEdge(gatewayNode, targetSocketNode, true, map[string]string{
		"label": lgb.addQuotation(fmt.Sprintf("forward <%s>", connectionID)),
		"color": color,
		"style": style,
	})

	fmt.Printf("添加容器间连接: %s -> %s -> %s (%s -> %s)\n",
		sourceSocketNode, gatewayNode, targetSocketNode, comm.SourcePod, targetContainerName)
}

// addExternalConnection 添加外部连接：Socket -> ExternalService
func (lgb *LocalGraphBasedBuilder) addExternalConnection(globalGraph *gographviz.Graph, sourceSocketNode string, comm *NetworkCommunication, label, color, style string) {
	// 创建外部服务节点
	targetNode := fmt.Sprintf("NetPeer##%s:%d", comm.DestIP, comm.DestPort)
	globalGraph.AddNode("G", lgb.addQuotation(targetNode), map[string]string{
		"shape": lgb.addQuotation("diamond"),
		"color": color,
	})

	// 添加连接：Socket -> ExternalService
	globalGraph.AddEdge(sourceSocketNode, lgb.addQuotation(targetNode), true, map[string]string{
		"label": lgb.addQuotation(label),
		"color": color,
		"style": style,
	})

	fmt.Printf("添加外部连接: %s -> %s (%s)\n", sourceSocketNode, targetNode, label)
}

// findSocketNodeInContainer 在容器中查找Socket节点，返回唯一的节点名
func (lgb *LocalGraphBasedBuilder) findSocketNodeInContainer(containerName string, globalGraph *gographviz.Graph) string {
	// 直接从映射中获取Socket节点名称
	if socketNodeName, exists := lgb.containerSocketNodes[containerName]; exists {
		fmt.Printf("找到Socket节点: %s (容器: %s)\n", socketNodeName, containerName)
		return socketNodeName
	}

	fmt.Printf("未找到容器 %s 的Socket节点\n", containerName)
	return ""
}

// saveGlobalGraph 保存全局图
func (lgb *LocalGraphBasedBuilder) saveGlobalGraph(graph *gographviz.Graph, outputPath string) error {
	fmt.Printf("保存全局图到: %s\n", outputPath)

	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("创建输出文件失败: %v", err)
	}
	defer file.Close()

	_, err = file.WriteString(graph.String())
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	fmt.Println("✅ 基于局部图的全局溯源图生成完成!")
	return nil
}

// addQuotation 添加引号
func (lgb *LocalGraphBasedBuilder) addQuotation(s string) string {
	return fmt.Sprintf("\"%s\"", s)
}
