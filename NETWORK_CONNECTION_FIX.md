# Alastor 网络连接修复方案

## 🚨 问题1：Pod内部错误的NetPeer节点

### 当前错误表现
```dot
// 错误：Pod内部包含外部服务节点
"product_purchase_authorize_cc_5cf7df585c_zrgrn_30" [ label="NetPeer##attackserver.openfaas-fn.svc.cluster.local:8888", shape="diamond" ];

// 同时全局也有相同节点
"NetPeer##attackserver.openfaas-fn.svc.cluster.local:8888" [ color=red, shape="diamond" ];
```

### 正确的网络流应该是
```
Container Process → Socket(127.0.0.1:34044) → Socket(0.0.0.0:3000) → Gateway → attackserver:8888
```

### 修复方案

#### 修复1：移除局部图中的外部NetPeer节点

```go
// 在 alastor.go 的网络通信解析中
func (p *AlastorParser) parseNetworkCommunication(comm *NetworkCommunication, graph *gographviz.Graph) {
    // 检查是否为外部服务
    if isExternalService(comm.DestIP) {
        // 不在局部图中创建外部服务的NetPeer节点
        // 只创建本地Socket节点，让全局图处理外部连接
        sourceSocket := p.findSocketForPort(comm.SourcePort)
        if sourceSocket != "" {
            // 记录连接信息，但不创建外部NetPeer
            p.recordExternalConnection(sourceSocket, comm)
        }
        return
    }
    
    // 只处理容器内部的网络连接
    // ...
}

func isExternalService(destIP string) bool {
    // 检查是否为外部服务
    return strings.Contains(destIP, "attackserver") ||
           strings.Contains(destIP, "gateway") ||
           !strings.HasPrefix(destIP, "127.0.0.1")
}
```

#### 修复2：在全局图中正确建立连接

```go
// 在 local_graph_based_builder.go 中
func (lgb *LocalGraphBasedBuilder) buildNetworkConnections(globalGraph *gographviz.Graph) {
    for _, comm := range lgb.networkComms {
        if isExternalConnection(comm) {
            // 建立正确的连接路径：Socket → Gateway → External
            sourceSocket := lgb.findContainerSocket(comm.ContainerName, comm.SourcePort)
            if sourceSocket != "" {
                // Socket → Gateway
                lgb.addSocketToGatewayConnection(globalGraph, sourceSocket, comm)
                
                // Gateway → External Service
                lgb.addGatewayToExternalConnection(globalGraph, comm)
            }
        }
    }
}
```

## 📊 Graph文件夹代码重构建议

### 当前问题总结
1. **alastor.go过长**：1469行，职责过多
2. **重复代码**：多个相似的网络解析函数
3. **网络连接错误**：Pod内部创建外部服务节点
4. **过时代码**：多个.bak文件和空文件

### 重构方案

#### 拆分alastor.go
```
alastor/
├── main.go                    # 程序入口 (50行)
├── local_graph_builder.go     # 局部图构建 (300行)
├── network_parser.go          # 网络解析 (400行)
├── syscall_handlers.go        # 系统调用处理 (300行)
├── utils.go                   # 工具函数 (100行)
└── types.go                   # 类型定义 (50行)
```

#### 清理过时代码
```bash
# 删除备份文件
rm graph/alastor/alastor.go.bak
rm graph/alastor/parser_global/trace-graph-builder-0*.go.bak
rm graph/alastor/parser_local/parser-local.go  # 空文件

# 删除过时目录
rm -rf graph/output-old/
rm -rf graph/dot/
```

## 🔧 of-watchdog代码分析

### 当前状态
- **main.go**: 1057行，功能完整但复杂
- **核心功能**: strace集成 + mitmproxy网络监控
- **关键修复**: 解决了mitmproxy竞态条件问题

### 代码质量评估

#### ✅ 良好的部分
1. **同步启动机制**：解决了竞态条件
2. **环境变量处理**：正确传递代理设置
3. **日志收集**：自动化日志发送
4. **错误处理**：完善的故障排除机制

#### ⚠️ 需要改进的部分
1. **代码重复**：多个备份文件存在
2. **函数过长**：main函数过于复杂
3. **硬编码**：部分配置写死

### 建议的重构
```
of-watchdog/
├── main.go                    # 主程序入口 (200行)
├── mitmproxy_manager.go       # mitmproxy管理 (150行)
├── strace_wrapper.go          # strace集成 (100行)
├── log_collector.go           # 日志收集 (150行)
└── config/                    # 配置管理
    └── environment.go         # 环境变量处理
```

## 📝 README更新建议

### of-watchdog/README-ALASTOR.md
当前文档已经非常详细和准确，包含：
- ✅ 完整的功能说明
- ✅ 详细的故障排除指南
- ✅ 技术架构图
- ✅ 与原版的差异对比

**建议的小幅更新**：
1. 添加重构后的代码结构说明
2. 更新性能优化建议
3. 添加最新的测试用例

### graph/README.md vs ARCHITECTURE.md
- **README.md**: 保持用户友好的快速入门指南
- **ARCHITECTURE.md**: 详细的技术架构文档（已创建）

## 🎯 立即执行的修复步骤

### 步骤1：修复网络连接问题
```go
// 在 alastor.go 中添加外部服务检测
func isExternalService(destIP string) bool {
    return strings.Contains(destIP, "attackserver") ||
           strings.Contains(destIP, "gateway") ||
           (!strings.HasPrefix(destIP, "127.0.0.1") && 
            !strings.HasPrefix(destIP, "10.") &&
            !strings.HasPrefix(destIP, "192.168."))
}
```

### 步骤2：清理过时代码
```bash
# 删除备份文件
find graph/ -name "*.bak" -delete
find openfaas-attack-funcs/of-watchdog/ -name "*.bak" -delete

# 删除空文件
rm graph/alastor/parser_local/parser-local.go
```

### 步骤3：测试验证
```bash
# 重新生成图
bash alastor-log-processor.sh logs/20250716_221335_hello-retail-single_attack2_1rps

# 检查是否还有Pod内部的attackserver节点
grep -n "attackserver" logs/*/reports/dot/global_local_based.dot
```

## 📈 预期修复效果

### 网络连接修复后
```dot
// 修复前：错误的Pod内部外部服务
"pod_30" [ label="NetPeer##attackserver:8888" ];

// 修复后：正确的连接路径
"pod_socket_3000" -> "Gateway##gateway:8080"
"Gateway##gateway:8080" -> "NetPeer##attackserver:8888"
```

### 代码质量提升
- 文件行数减少30%
- 重复代码消除
- 模块职责清晰
- 维护成本降低

这个修复方案将显著提升Alastor的网络连接表示准确性和代码可维护性。
